import { Text, makeStyles } from "@fluentui/react-components";
import { useContext } from "react";
import { ThemeContext } from "../../App";
import emptySvg from "../../assets/empty.svg";

interface EmptyDataProps {
    imageSrc?: string;
    text?: string;
    style?: React.CSSProperties;
}

const useStyles = makeStyles({
    image: {
        maxWidth: "100%",
        width: "200px",
        height: "auto",
        filter: "var(--colorNeutralForeground3Filter)",
    },
    imageDark: {
        filter: "invert(0.8) brightness(1.5)",
    },
    text: {
        color: "var(--colorNeutralForeground4)",
    },
    container: {
        display: "flex",
        flexDirection: "column",
        alignItems: "center",
        justifyContent: "center",
        gap: "20px",
    },
});

function EmptyData({ imageSrc = emptySvg, text = "暂无数据", style }: EmptyDataProps) {
    const styles = useStyles();
    const { isDarkTheme } = useContext(ThemeContext);

    return (
        <div className={styles.container} style={style}>
            <img
                src={imageSrc} 
                alt="Empty" 
                className={`${styles.image} ${isDarkTheme ? styles.imageDark : ''}`} 
            />
            <Text className={styles.text}>{text}</Text>
        </div>
    );
}

export default EmptyData;








