/* eslint-disable @typescript-eslint/no-explicit-any */
declare global {
    interface Window {
        Config: {
            AIService: AIServiceConfig;
        };
        PromptTemplates: PromptTemplate[];
        PromptContext: PromptContext;
    }
}

export interface AIServiceConfig {
    url: string;
    key: string;
    model: string;
    stream?: boolean;
    streamType: string;
    multimodal?: boolean;
}

export interface PromptContext {
    context: string;
    onInit?: () => void;
}

export interface PromptTemplate {
    name: string;
    title: string;
    prompt: string;
    system?: string;
    onInit?: (context: any) => void;
    onResult?: (result: any) => void;
    onApply?: (data: CodeBlockData) => void;
}

export interface CodeBlockData {
    blockId: string;
    params: URLSearchParams;
    content: string;
}

export interface ChatImage {
    id: string;
    file: File;
    base64: string;
    preview: string;
}

export interface ChatItem {
    promptTemplate: PromptTemplate;
    reasoningHtml: string;
    answerHtml: string;
    startTime?: number;
    endTime?: number;
    isThinking?: boolean;
    images?: ChatImage[];
}

export {};


