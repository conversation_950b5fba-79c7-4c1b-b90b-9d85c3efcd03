:root {
    /* 暗色主题变量 */
    --dark-bg-gradient: linear-gradient(135deg, #1e1e1e 0%, #2d2d2d 100%);
    --dark-text-primary: #ffffff;
    --dark-text-secondary: #e0e0e0;
    --dark-text-muted: #b0b0b0;
    --dark-text-description: #c0c0c0;
    --dark-container-bg: rgba(45, 45, 45, 0.8);
    --dark-container-border: rgba(255, 255, 255, 0.1);
    --dark-features-bg: rgba(255, 255, 255, 0.05);
    --dark-btn-secondary-border: rgba(255, 255, 255, 0.2);
    --dark-btn-secondary-hover-bg: rgba(255, 255, 255, 0.1);
    --dark-btn-secondary-hover-border: rgba(255, 255, 255, 0.3);
    --dark-footer-border: rgba(255, 255, 255, 0.1);
    --dark-footer-text: #888;

    /* 亮色主题变量 */
    --light-bg-gradient: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    --light-text-primary: #212529;
    --light-text-secondary: #495057;
    --light-text-muted: #6c757d;
    --light-text-description: #495057;
    --light-container-bg: rgba(255, 255, 255, 0.9);
    --light-container-border: rgba(0, 0, 0, 0.1);
    --light-features-bg: rgba(74, 158, 255, 0.05);
    --light-btn-secondary-border: rgba(0, 0, 0, 0.2);
    --light-btn-secondary-hover-bg: rgba(0, 0, 0, 0.05);
    --light-btn-secondary-hover-border: rgba(0, 0, 0, 0.3);
    --light-footer-border: rgba(0, 0, 0, 0.1);
    --light-footer-text: #6c757d;
}

.subscription-page {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
    min-height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
    line-height: 1.5;
    padding: 20px;
    transition: all 0.3s ease;
}

/* 暗色主题样式 */
.subscription-page.dark {
    background: var(--dark-bg-gradient);
    color: var(--dark-text-secondary);
}

.subscription-page.dark .subscription-container {
    background: var(--dark-container-bg);
    border: 1px solid var(--dark-container-border);
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
}

.subscription-page.dark .title {
    color: var(--dark-text-primary);
}

.subscription-page.dark .subtitle {
    color: var(--dark-text-muted);
}

.subscription-page.dark .description {
    color: var(--dark-text-description);
}

.subscription-page.dark .features {
    background: var(--dark-features-bg);
}

.subscription-page.dark .features h3 {
    color: var(--dark-text-primary);
}

.subscription-page.dark .features li {
    color: #d0d0d0;
}

.subscription-page.dark .btn-secondary {
    color: var(--dark-text-muted);
    border: 1px solid var(--dark-btn-secondary-border);
}

.subscription-page.dark .btn-secondary:hover {
    background: var(--dark-btn-secondary-hover-bg);
    color: var(--dark-text-primary);
    border-color: var(--dark-btn-secondary-hover-border);
}

.subscription-page.dark .footer {
    border-top: 1px solid var(--dark-footer-border);
    color: var(--dark-footer-text);
}

/* 亮色主题样式 */
.subscription-page.light {
    background: var(--light-bg-gradient);
    color: var(--light-text-secondary);
}

.subscription-page.light .subscription-container {
    background: var(--light-container-bg);
    border: 1px solid var(--light-container-border);
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

.subscription-page.light .title {
    color: var(--light-text-primary);
}

.subscription-page.light .subtitle {
    color: var(--light-text-muted);
}

.subscription-page.light .description {
    color: var(--light-text-description);
}

.subscription-page.light .features {
    background: var(--light-features-bg);
}

.subscription-page.light .features h3 {
    color: var(--light-text-primary);
}

.subscription-page.light .features li {
    color: var(--light-text-secondary);
}

.subscription-page.light .btn-secondary {
    color: var(--light-text-muted);
    border: 1px solid var(--light-btn-secondary-border);
}

.subscription-page.light .btn-secondary:hover {
    background: var(--light-btn-secondary-hover-bg);
    color: var(--light-text-primary);
    border-color: var(--light-btn-secondary-hover-border);
}

.subscription-page.light .footer {
    border-top: 1px solid var(--light-footer-border);
    color: var(--light-footer-text);
}

.subscription-container {
    max-width: 500px;
    padding: 32px 24px;
    border-radius: 16px;
    text-align: center;
    transition: all 0.3s ease;
}

/* 页面过渡动画 */
.subscription-container {
    transition: opacity 0.4s ease-out, transform 0.4s ease-out;
}

.subscription-container.fade-out {
    opacity: 0;
    transform: translateY(-20px);
}

/* 按钮禁用状态 */
.btn.disabled {
    opacity: 0.6;
    cursor: not-allowed;
    pointer-events: none;
}

.subscription-page.dark .btn.disabled {
    opacity: 0.5;
}

.subscription-page.light .btn.disabled {
    opacity: 0.6;
}

/* 按钮过渡效果增强 */
.btn {
    transition: all 0.3s ease, opacity 0.4s ease;
}

.icon {
    width: 64px;
    height: 64px;
    margin: 0 auto 16px;
    background: linear-gradient(135deg, #4a9eff 0%, #6366f1 100%);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 28px;
}

.title {
    font-size: 24px;
    font-weight: 600;
    margin-bottom: 12px;
    transition: color 0.3s ease;
}

.subtitle {
    font-size: 16px;
    margin-bottom: 20px;
    transition: color 0.3s ease;
}

.description {
    font-size: 14px;
    margin-bottom: 20px;
    line-height: 1.6;
    transition: color 0.3s ease;
}

.features {
    text-align: left;
    margin: 20px 0;
    padding: 16px;
    border-radius: 12px;
    border-left: 4px solid #4a9eff;
    transition: background-color 0.3s ease;
}

.features h3 {
    font-size: 16px;
    margin-bottom: 12px;
    display: flex;
    align-items: center;
    transition: color 0.3s ease;
}

.features h3::before {
    content: "✨";
    margin-right: 8px;
}

.features ul {
    list-style: none;
    padding: 0 0 0 5px;
}

.features li {
    margin-bottom: 6px;
    padding-left: 20px;
    position: relative;
    font-size: 14px;
    transition: color 0.3s ease;
}

.features li::before {
    content: "•";
    color: #4a9eff;
    position: absolute;
    left: 0;
    font-weight: bold;
}

.buttons {
    display: flex;
    gap: 16px;
    justify-content: center;
    flex-wrap: nowrap;
    margin-top: 20px;
}

.btn {
    padding: 12px 24px;
    border: none;
    border-radius: 8px;
    font-size: 16px;
    font-weight: 500;
    cursor: pointer;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    gap: 8px;
    transition: all 0.3s ease;
    min-width: 100px;
    justify-content: center;
    white-space: nowrap;
}

.btn-primary {
    background: linear-gradient(135deg, #4a9eff 0%, #6366f1 100%);
    color: white;
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(74, 158, 255, 0.3);
}

.btn-secondary {
    background: transparent;
    transition: all 0.3s ease;
}

.footer {
    margin-top: 20px;
    padding-top: 16px;
    font-size: 13px;
    transition: all 0.3s ease;
}

@media (max-width: 350px) {
    .subscription-container {
        margin: 20px;
        padding: 32px 24px;
    }
    
    .btn {
        width: 100%;
        max-width: 200px;
    }
}
