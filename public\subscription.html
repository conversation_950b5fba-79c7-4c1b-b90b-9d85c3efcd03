<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI服务订阅 - Report Assist</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
            background: linear-gradient(135deg, #1e1e1e 0%, #2d2d2d 100%);
            color: #e0e0e0;
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            line-height: 1.6;
        }

        .container {
            max-width: 500px;
            padding: 32px 24px;
            background: rgba(45, 45, 45, 0.8);
            border-radius: 16px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
            border: 1px solid rgba(255, 255, 255, 0.1);
            text-align: center;
        }

        .icon {
            width: 64px;
            height: 64px;
            margin: 0 auto 16px;
            background: linear-gradient(135deg, #4a9eff 0%, #6366f1 100%);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 28px;
        }

        .title {
            font-size: 24px;
            font-weight: 600;
            color: #ffffff;
            margin-bottom: 12px;
        }

        .subtitle {
            font-size: 16px;
            color: #b0b0b0;
            margin-bottom: 20px;
        }

        .description {
            font-size: 14px;
            color: #c0c0c0;
            margin-bottom: 20px;
            line-height: 1.6;
        }

        .features {
            text-align: left;
            margin: 20px 0;
            padding: 16px;
            background: rgba(255, 255, 255, 0.05);
            border-radius: 12px;
            border-left: 4px solid #4a9eff;
        }

        .features h3 {
            color: #ffffff;
            font-size: 16px;
            margin-bottom: 12px;
            display: flex;
            align-items: center;
        }

        .features h3::before {
            content: "✨";
            margin-right: 8px;
        }

        .features ul {
            list-style: none;
        }

        .features li {
            margin-bottom: 6px;
            padding-left: 20px;
            position: relative;
            color: #d0d0d0;
            font-size: 14px;
        }

        .features li::before {
            content: "•";
            color: #4a9eff;
            position: absolute;
            left: 0;
            font-weight: bold;
        }

        .buttons {
            display: flex;
            gap: 16px;
            justify-content: center;
            flex-wrap: wrap;
            margin-top: 20px;
        }

        .btn {
            padding: 12px 24px;
            border: none;
            border-radius: 8px;
            font-size: 16px;
            font-weight: 500;
            cursor: pointer;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 8px;
            transition: all 0.3s ease;
            min-width: 140px;
            justify-content: center;
        }

        .btn-primary {
            background: linear-gradient(135deg, #4a9eff 0%, #6366f1 100%);
            color: white;
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(74, 158, 255, 0.3);
        }

        .btn-secondary {
            background: transparent;
            color: #b0b0b0;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .btn-secondary:hover {
            background: rgba(255, 255, 255, 0.1);
            color: #ffffff;
            border-color: rgba(255, 255, 255, 0.3);
        }

        .footer {
            margin-top: 20px;
            padding-top: 16px;
            border-top: 1px solid rgba(255, 255, 255, 0.1);
            font-size: 13px;
            color: #888;
        }

        @media (max-width: 600px) {
            .container {
                margin: 20px;
                padding: 32px 24px;
            }
            
            .buttons {
                flex-direction: column;
                align-items: center;
            }
            
            .btn {
                width: 100%;
                max-width: 200px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="icon">
            <svg width="36" height="36" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                <defs>
                    <pattern id="circuit" patternUnits="userSpaceOnUse" width="6" height="6">
                        <path d="M0,3 L3,3 M3,0 L3,6" stroke="currentColor" stroke-width="0.2" opacity="0.15"/>
                    </pattern>
                </defs>
                <rect width="24" height="24" fill="url(#circuit)"/>
                <path d="M2 20 L6 8 L10 20 M3.5 16 L8.5 16" 
                      stroke="currentColor" 
                      stroke-width="2.5" 
                      stroke-linecap="round" 
                      stroke-linejoin="round" 
                      fill="none"/>
                <path d="M14 8 L20 8 M17 8 L17 20 M14 20 L20 20" 
                      stroke="currentColor" 
                      stroke-width="2.5" 
                      stroke-linecap="round" 
                      stroke-linejoin="round" 
                      fill="none"/>
                <path d="M6 6 Q12 4 17 6" stroke="currentColor" stroke-width="1" opacity="0.5" fill="none"/>
                <circle cx="6" cy="6" r="0.8" fill="currentColor" opacity="0.6"/>
                <circle cx="17" cy="6" r="0.8" fill="currentColor" opacity="0.6"/>
                <circle cx="10" cy="5" r="0.6" fill="currentColor" opacity="0.7">
                    <animate attributeName="opacity" values="0.3;1;0.3" dur="2.5s" repeatCount="indefinite"/>
                </circle>
                <circle cx="14" cy="5" r="0.6" fill="currentColor" opacity="0.7">
                    <animate attributeName="opacity" values="1;0.3;1" dur="2.5s" repeatCount="indefinite"/>
                </circle>
                <rect x="7" y="22" width="10" height="1.5" rx="0.75" fill="currentColor" opacity="0.6"/>
                <rect x="8" y="22.2" width="2" height="1.1" fill="white" opacity="0.3"/>
                <rect x="10.5" y="22.2" width="3" height="1.1" fill="white" opacity="0.3"/>
                <rect x="14" y="22.2" width="2" height="1.1" fill="white" opacity="0.3"/>
            </svg>
        </div>
        
        <h1 class="title">温馨提示</h1>
        <p class="subtitle">AI智能服务需要订阅后使用</p>
        
        <p class="description">
            感谢您对我们AI医疗影像报告助手的关注！<br/>为了提供更优质、稳定的AI服务体验，我们的智能分析功能需要订阅后才能使用。
        </p>

        <div class="features">
            <h3>订阅后您将享受到：</h3>
            <ul>
                <li>基于患者病情的智能对话分析和专业建议</li>
                <li>智能医学影像报告审查与纠错</li>
                <li>个性化报告内容优化建议与指导</li>
                <li>持续的功能更新和优化</li>
                <li>7x24小时稳定服务保障</li>
            </ul>
        </div>

        <div class="buttons">
            <a href="#" class="btn btn-primary" onclick="handleSubscribe()">了解订阅方案</a>
            <!-- <a href="#" class="btn btn-secondary">我要试用</a> -->
        </div>

        <div class="footer">
            <p>如有任何疑问，欢迎随时联系我们的客服团队</p>
        </div>
    </div>

    <script>
        function handleSubscribe() {
            window.open('https://www.myfilm.cc', '_blank');
        }

        document.addEventListener('DOMContentLoaded', function() {
            const container = document.querySelector('.container');
            container.style.opacity = '0';
            container.style.transform = 'translateY(20px)';
            
            setTimeout(() => {
                container.style.transition = 'all 0.6s ease';
                container.style.opacity = '1';
                container.style.transform = 'translateY(0)';
            }, 100);
        });
    </script>
</body>
</html>
