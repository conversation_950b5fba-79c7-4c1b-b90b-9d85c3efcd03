/* eslint-disable @typescript-eslint/no-explicit-any */
import MarkdownIt from 'markdown-it';
import hljs from 'highlight.js';
import 'highlight.js/styles/github.css';
import type { CodeBlockData } from '../global';

export class Markdown {
    private md: MarkdownIt;

    constructor() {
        //hljs.highlightAll();
        this.md = new MarkdownIt({
            // html: true,          // 允许HTML标签
            // linkify: true,       // 自动识别URL并转为链接
            typographer: true,   // 启用一些语言中立的替换和引号美化
            highlight: this.highlightCode
        });

        this.setupCodeBlockRenderer();
    }

    // 代码高亮处理函数
    private highlightCode(str: string, lang: string): string {
        let highlighted = '';
        if (lang && hljs.getLanguage(lang)) {
            try {
                highlighted = hljs.highlight(str, { language: lang }).value;
            } catch (err) {
                console.error("高亮错误:", err);
                highlighted = str;
            }
        } else {
            try {
                highlighted = hljs.highlightAuto(str).value;
            } catch (err) {
                console.error("自动高亮失败:", err);
                highlighted = str;
            }
        }

        return highlighted;
    }

    // 设置代码块渲染器
    private setupCodeBlockRenderer(): void {
        const defaultFence = this.md.renderer.rules.fence || ((tokens, idx, options, env, slf) => {
            return slf.renderToken(tokens, idx, options);
        });

        this.md.renderer.rules.fence = (tokens, idx, options, env, slf) => {
            const token = tokens[idx];
            const langInfo = token.info || '代码';

            // 解析语言和标题
            const [lang, params] = langInfo.split('?');

            // 修改token.info以便highlight函数能正确处理
            token.info = lang;

            // 获取默认渲染的代码块HTML
            const codeHtml = defaultFence(tokens, idx, options, env, slf);

            // 渲染代码块
            return this.renderCodeBlock(codeHtml, token, lang.toLocaleLowerCase(), params);
        };
    }

    // 渲染带有工具栏的代码块
    private renderCodeBlock(
        codeHtml: string,
        token: any,
        lang: string,
        paramsString: string,
    ): string {
        // 添加唯一ID以便于事件处理
        const blockId = `code-block-${Date.now()}-${Math.floor(Math.random() * 1000)}`;
        const params = new URLSearchParams(paramsString || '');
        const title = params.get('title') || '';
        const apply = params.get('code-apply-btn') === 'true';
        const compare = params.get('code-compare-btn') === 'true';

        return /* html */`
            <div class="code-container${title ? ' has-title' : ''}${lang === 'text' ? ' text-block' : ''}" id="${blockId}">
                <div class="code-header" params-string="${paramsString ? encodeURIComponent(paramsString) : ''}">
                    <span>${title || lang}</span>
                    <div class="button-group">
                        ${compare ? '<button class="code-compare-btn fui-button fui-button--small">比较</button>' : ''}
                        ${apply ? '<button class="code-apply-btn fui-button fui-button--small" title="应用">应用</button>' : ''}
                        <button class="code-copy-btn fui-button fui-button--small" title="复制" data-code="${encodeURIComponent(token.content)}">复制</button>
                    </div>
                </div>
                ${codeHtml}
            </div>
            
            <style>
            /* 解决 Fluent-UI 行高异常的问题，重置默认行高 */
            h1, h2, h3, h4, h5, h6 {
                line-height: 1.5 !important;
                margin: 0.67em 0 !important;
            }
            h1 {
                font-size: 2em !important;
            }
            h2 {
                font-size: 1.5em !important;
            }
            h3 {
                font-size: 1.17em !important;
            }
            
            .code-container {
                margin-bottom: 20px;
            }

            .code-header {
                display: flex;
                justify-content: space-between;
                align-items: center;
                padding: 5px 10px;
                border-radius: 5px 5px 0 0;
                border: 1px solid #ddd;
                border-bottom: none;
                font-size: 14px;
                background-color: var(--colorNeutralCardBackgroundSelected);
                color: var(--colorNeutralForeground1);
            }

            .code-header > span {
                flex: 1;
                min-width: 0;
                white-space: nowrap;
                overflow: hidden;
                word-wrap: break-word;
                text-overflow: ellipsis;
            }

            /* 带标题的代码块样式 */
            .has-title .code-header {
                background-color: var(--colorNeutralCardBackgroundSelected);
            }

            .button-group {
                display: flex;
                gap: 4px;
            }

            .fui-button {
                padding: 4px 8px;
                border: 1px solid var(--colorNeutralStroke1);
                border-radius: var(--borderRadiusSmall);
                background-color: var(--colorNeutralBackground1);
                color: var(--colorNeutralForeground1);
                font-size: 12px;
                cursor: pointer;
                transition: all 0.1s ease;
            }

            .fui-button:hover {
                background-color: var(--colorNeutralBackground1Hover);
                border-color: var(--colorNeutralStroke1Hover);
            }

            .fui-button:active {
                background-color: var(--colorNeutralBackground1Pressed);
            }

            .fui-button:disabled {
                opacity: 0.6;
                cursor: not-allowed;
            }

            .fui-button--success {
                color: var(--colorNeutralForegroundOnBrand) !important;
                background-color: var(--colorStatusSuccessBackground3) !important;
                border-color: var(--colorStatusSuccessBorderActive) !important;
            }

            .fui-button--warning {
                color: var(--colorNeutralForeground1) !important;
                background-color: var(--colorStatusWarningBackground3) !important;
                border-color: var(--colorStatusWarningBorderActive) !important;
            }

            /* 文本代码块自动换行 */
            .text-block pre code {
                white-space: pre-wrap;       /* 保留空格，但允许换行 */
                word-wrap: break-word;       /* 允许长单词断行 */
                line-height: 1.5;            /* 增加行高，提高可读性 */
                font-family: var(--fontFamilyBase);
                font-size: var(--fontSizeBase);
            }

            pre {
                background-color: var(--colorNeutralCardBackground);
                padding: 16px;
                border-radius: 0 0 5px 5px;
                overflow: auto;
                border: 1px solid #ddd;
                border-top: none;
            }

            code {
                font-family: 'SFMono-Regular', Consolas, 'Liberation Mono', Menlo, monospace;
                font-size: 85%;
            }
            </style>
        `;
    }

    // 渲染Markdown
    public render(markdown: string): string {
        return this.md.render(markdown);
    }

    // 绑定事件处理器
    public bindEventHandlers(event?: { onApplyCallback?: (data: CodeBlockData) => void }): void {
        // 复制按钮
        const copyButtons = document.querySelectorAll('.code-copy-btn');
        copyButtons.forEach((btn) => {
            btn.addEventListener('click', () => {
                this.copy(btn as HTMLButtonElement, decodeURIComponent(btn.getAttribute('data-code') || ''));
            });
        });

        // 应用按钮
        const applyButtons = document.querySelectorAll('.code-apply-btn');
        applyButtons.forEach((btn) => {
            btn.addEventListener('click', () => {
                this.apply(btn as HTMLButtonElement, event);
            });
        });
    }

    // 复制的降级方案，兼容更多的浏览器
    private legacyCopy(text: string): Promise<void> {
        return new Promise((resolve, reject) => {
            const textarea = document.createElement('textarea');
            textarea.value = text;
            document.body.appendChild(textarea);
            textarea.select();

            try {
                const success = document.execCommand('copy');
                document.body.removeChild(textarea);
                if (success) {
                    resolve();
                } else {
                    reject(new Error('execCommand failed'));
                }
            } catch (err) {
                document.body.removeChild(textarea);
                reject(err);
            }
        });
    }

    private updateButtonState(button: HTMLButtonElement, text: string, style: string): void {
        button.disabled = true;
        button.classList.add(style);

        setTimeout(() => {
            button.disabled = false;
            button.classList.remove(style);
        }, 2000);
    }

    // 复制代码块到剪贴板
    private async copy(button: HTMLButtonElement, text: string): Promise<void> {
        if (button.disabled) {
            return;
        }

        try {
            if (navigator.clipboard?.writeText) {
                await navigator.clipboard.writeText(text);  // 优先使用现代API
            } else {
                await this.legacyCopy(text);    // 降级方案
            }
            this.updateButtonState(button, 'Copy', 'fui-button--success');
        } catch (error) {
            console.error('CopyFailed:', error);
            this.updateButtonState(button, 'Copy Failed', 'fui-button--warning');
        }
    }

    // 应用代码块
    private async apply(button: HTMLButtonElement, event?: { onApplyCallback?: (data: CodeBlockData) => void }): Promise<void> {
        if (button.disabled) {
            return;
        }

        const container = button.closest('.code-container');
        if (!container) {
            return;
        }

        const params = new URLSearchParams(decodeURIComponent(container.querySelector('.code-header')?.getAttribute('params-string') || ''));
        const content = container.querySelector('pre code')?.textContent || '';
        const data: CodeBlockData = {
            blockId: container.id,
            params: params,
            content: content,
        };

        // 调用回调函数
        if (event?.onApplyCallback) {
            this.updateButtonState(button, 'Apply', 'fui-button--success');
            event.onApplyCallback(data);
        }
    }
}

export default Markdown;
