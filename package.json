{"name": "report-assist", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc -b tsconfig.build.json && vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@fluentui/react-components": "^9.67.0", "@fluentui/react-icons": "^2.0.306", "highlight.js": "^11.11.1", "markdown-it": "^14.1.0", "react": "^18.3.1", "react-dom": "^18.3.1", "react-router-dom": "^6.28.0"}, "devDependencies": {"@eslint/js": "^9.30.1", "@types/markdown-it": "^14.1.2", "@types/node": "^24.1.0", "@types/react": "^18.3.12", "@types/react-dom": "^18.3.1", "@vitejs/plugin-react": "^4.6.0", "eslint": "^9.30.1", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.20", "globals": "^16.3.0", "typescript": "~5.8.3", "typescript-eslint": "^8.35.1", "vite": "^7.0.4"}}