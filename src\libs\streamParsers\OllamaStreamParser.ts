import type { IStreamParser } from './IStreamParser';

export class OllamaStreamParser implements IStreamParser {
    private reasoningBuffer = '';   // 推理内容
    private answerBuffer = '';      // 回答内容    
    private inThinkTag = false;

    parse(chunk: string): { reasoning: string, answer: string } {
        try {
            // Ollama 流式响应格式处理
            // 格式：{"message": ..., "done": ...}\n{"message": ..., "done": ...}\n{"done": true}\n
            const lines = chunk.split('\n');
            for (const line of lines) {
                // 跳过空行
                if (line.trim() === '')
                    continue;

                // 处理数据行
                const data = JSON.parse(line);

                // 提取内容 - Ollama格式
                const content = data.message?.content || '';
                if (content) {
                    // 通过<think>标签，判断推理内容开始
                    if (content === "<think>") {
                        this.inThinkTag = true;
                        continue;
                    }

                    if (this.inThinkTag) {
                        // 提取推理内容
                        this.reasoningBuffer += content;
                    } else {
                        // 提取回答内容
                        this.answerBuffer += content;
                    }

                    // 通过<think>标签，判断推理内容结束
                    if (content === "</think>") {
                        this.inThinkTag = false;
                        continue;
                    }
                }

                if (data.done === true) break;
            }
            
            return { reasoning: this.reasoningBuffer, answer: this.answerBuffer };
        } catch (error) {
            console.error('Ollama 流解析错误:', error);
            return { reasoning: this.reasoningBuffer, answer: this.answerBuffer };
        }
    }
}