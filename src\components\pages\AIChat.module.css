.container {
    height: 100%;
    display: flex;
    flex-direction: column;
    background-color: var(--colorNeutralBackground1);
}

.main {
    flex: 1;
    overflow: auto;
    padding: 20px;
    /* 滚动条样式 - 支持暗色主题 */
    scrollbar-width: thin;
    scrollbar-color: var(--colorNeutralStroke2) var(--colorNeutralBackground1);
}

/* Webkit 浏览器滚动条样式 */
.main::-webkit-scrollbar {
    width: 8px;
    height: 8px;
}

.main::-webkit-scrollbar-track {
    background: var(--colorNeutralBackground1);
    border-radius: 4px;
}

.main::-webkit-scrollbar-thumb {
    background: var(--colorNeutralStroke2);
    border-radius: 4px;
    border: 1px solid var(--colorNeutralBackground1);
}

.main::-webkit-scrollbar-thumb:hover {
    background: var(--colorNeutralStroke1);
}

.main::-webkit-scrollbar-thumb:active {
    background: var(--colorNeutralStroke1Hover);
}

.main::-webkit-scrollbar-corner {
    background: var(--colorNeutralBackground1);
}

.footer {
    display: flex;
    flex-direction: column;
    gap: 5px;
    background-color: var(--colorNeutralBackground2);
    border-radius: var(--borderRadiusLarge);
    border: 1px solid var(--colorNeutralStroke2);
    margin: 10px;
    padding: 10px;
}

.footer:hover {
    border-color: var(--colorBrandStroke1);
    background-color: var(--colorNeutralBackground2Hover);
}

.textarea {
    width: 100%;
    resize: none;
    border: none;
    outline: none;
    font-family: inherit;
    background-color: transparent;
    color: var(--colorNeutralForeground1);
}

.textarea::placeholder {
    color: var(--colorNeutralForeground3);
    font-style: italic;
}

.toolbar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    /* padding-top: 8px; */
}

.leftToolbar {
    display: flex;
    align-items: center;
    gap: 12px;
}

.attachmentBar {
    padding: 12px 0 8px 0;
    border-top: 1px solid var(--colorNeutralStroke3);
    /* margin-top: 8px; */
}

.imagePreviewContainer {
    display: flex;
    gap: 8px;
    flex-wrap: wrap;
    align-items: center;
}

.imagePreview {
    position: relative;
    width: 60px;
    height: 60px;
    border-radius: var(--borderRadiusMedium);
    overflow: hidden;
    border: 1px solid var(--colorNeutralStroke2);
    background-color: var(--colorNeutralBackground2);
}

.previewImage {
    width: 100%;
    height: 100%;
    object-fit: cover;
    cursor: pointer;
}

.removeImageButton {
    position: absolute;
    top: -4px;
    right: -4px;
    width: 20px;
    height: 20px;
    min-width: 20px;
    border-radius: 50%;
    background-color: var(--colorNeutralBackground1);
    border: 1px solid var(--colorNeutralStroke2);
    box-shadow: var(--shadow4);
}

.removeImageButton:hover {
    background-color: var(--colorNeutralBackground1Hover);
}

.uploadingIndicator {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 60px;
    height: 60px;
    border: 1px dashed var(--colorNeutralStroke2);
    border-radius: var(--borderRadiusMedium);
    color: var(--colorNeutralForeground3);
    background-color: var(--colorNeutralBackground2);
}

.hiddenFileInput {
    display: none;
}

.chatImages {
    display: flex;
    gap: 8px;
    margin-top: 8px;
    flex-wrap: wrap;
}

.chatImageThumbnail {
    width: 80px;
    height: 80px;
    object-fit: cover;
    border-radius: var(--borderRadiusMedium);
    border: 1px solid var(--colorNeutralStroke2);
    cursor: pointer;
    transition: transform 0.2s ease;
}

.chatImageThumbnail:hover {
    transform: scale(1.05);
    box-shadow: var(--shadow8);
}

/* 响应式设计 */
@media (max-width: 768px) {
    .imagePreviewContainer {
        gap: 6px;
    }
    
    .imagePreview {
        width: 50px;
        height: 50px;
    }
    
    .chatImageThumbnail {
        width: 60px;
        height: 60px;
    }
    
    .uploadingIndicator {
        width: 50px;
        height: 50px;
    }
}

.chatItem {
    margin-bottom: 20px;
}

.promptTitle {
    display: flex;
    justify-content: flex-end;
    margin-bottom: var(--spacingVerticalL);
}

.promptTitle > * {
    background-color: var(--colorBrandBackground);
    color: var(--colorNeutralForegroundOnBrand);
    padding: 12px 16px;
    border-radius: var(--borderRadiusLarge);
    max-width: 80%;
    box-shadow: var(--shadow4);
    font-size: 14px;
    line-height: 1.4;
    word-wrap: break-word;
    white-space: pre-wrap;
    position: relative;
}

.promptTitle > *::before {
    content: '';
    position: absolute;
    bottom: -6px;
    right: 16px;
    width: 0;
    height: 0;
    border-left: 6px solid transparent;
    border-right: 6px solid transparent;
    border-top: 6px solid var(--colorBrandBackground);
}

.reasoning {
    margin: 0;
    padding: 10px;
    color: var(--colorNeutralForeground4);
    background-color: var(--colorNeutralBackground3);
    border-bottom-left-radius: var(--borderRadiusLarge);
    border-bottom-right-radius: var(--borderRadiusLarge);
}

.answer {
    margin-top: 10px;
    color: var(--colorNeutralForeground1);
}

.empty {
    display: flex;
    flex-direction: column;
    padding: 20px;
    height: 100%;
}

.templateList {
    margin-top: 20px;
}

.accordionHeader button:first-child {
    display: flex;
    align-items: center;
    border-top-left-radius: var(--borderRadiusLarge);
    border-top-right-radius: var(--borderRadiusLarge);
    background-color: var(--colorNeutralBackground5);
    color: var(--colorNeutralForeground1);
}

.accordionHeader button:first-child:hover {
    background-color: var(--colorNeutralBackground2Hover);
}

.accordionHeader button:first-child:active {
    background-color: var(--colorNeutralBackground2Pressed);
}

.container :global(.fui-AccordionItem .fui-AccordionPanel) {
    margin: 0;
    background-color: var(--colorNeutralBackground1);
}



