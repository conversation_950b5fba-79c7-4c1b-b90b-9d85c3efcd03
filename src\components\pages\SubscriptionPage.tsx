import { useNavigate } from 'react-router-dom';
import { useContext, useState, useTransition } from 'react';
import { ThemeContext } from '../../App';
import './SubscriptionPage.css';

function SubscriptionPage() {
    const navigate = useNavigate();
    const { isDarkTheme } = useContext(ThemeContext);
    const [isTransitioning, setIsTransitioning] = useState(false);
    const [isPending, startTransition] = useTransition();

    const handleSubscribe = () => {
        window.open('https://www.myfilm.cc', '_blank');
    };

    const handleTrial = () => {
        if (isTransitioning || isPending) return;
        
        setIsTransitioning(true);
        
        // 开始淡出动画
        const container = document.querySelector('.subscription-container') as HTMLElement;
        if (container) {
            container.classList.add('fade-out');
        }
        
        // 等待动画完成后导航
        setTimeout(() => {
            startTransition(() => {
                navigate('/app');
            });
        }, 400);
    };

    return (
        <div className={`subscription-page ${isDarkTheme ? 'dark' : 'light'}`}>
            <div className="subscription-container">
                <div className="icon">
                    <svg width="36" height="36" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <defs>
                            <pattern id="circuit" patternUnits="userSpaceOnUse" width="6" height="6">
                                <path d="M0,3 L3,3 M3,0 L3,6" stroke="currentColor" strokeWidth="0.2" opacity="0.15"/>
                            </pattern>
                        </defs>
                        <rect width="24" height="24" fill="url(#circuit)"/>
                        <path d="M2 20 L6 8 L10 20 M3.5 16 L8.5 16" 
                              stroke="currentColor" 
                              strokeWidth="2.5" 
                              strokeLinecap="round" 
                              strokeLinejoin="round" 
                              fill="none"/>
                        <path d="M14 8 L20 8 M17 8 L17 20 M14 20 L20 20" 
                              stroke="currentColor" 
                              strokeWidth="2.5" 
                              strokeLinecap="round" 
                              strokeLinejoin="round" 
                              fill="none"/>
                        <path d="M6 6 Q12 4 17 6" stroke="currentColor" strokeWidth="1" opacity="0.5" fill="none"/>
                        <circle cx="6" cy="6" r="0.8" fill="currentColor" opacity="0.6"/>
                        <circle cx="17" cy="6" r="0.8" fill="currentColor" opacity="0.6"/>
                        <circle cx="10" cy="5" r="0.6" fill="currentColor" opacity="0.7">
                            <animate attributeName="opacity" values="0.3;1;0.3" dur="2.5s" repeatCount="indefinite"/>
                        </circle>
                        <circle cx="14" cy="5" r="0.6" fill="currentColor" opacity="0.7">
                            <animate attributeName="opacity" values="1;0.3;1" dur="2.5s" repeatCount="indefinite"/>
                        </circle>
                        <rect x="7" y="22" width="10" height="1.5" rx="0.75" fill="currentColor" opacity="0.6"/>
                        <rect x="8" y="22.2" width="2" height="1.1" fill="white" opacity="0.3"/>
                        <rect x="10.5" y="22.2" width="3" height="1.1" fill="white" opacity="0.3"/>
                        <rect x="14" y="22.2" width="2" height="1.1" fill="white" opacity="0.3"/>
                    </svg>
                </div>
                
                <h1 className="title">温馨提示</h1>
                <p className="subtitle">AI智能服务需要订阅后使用</p>
                
                <p className="description">
                    感谢您对我们AI医疗影像报告助手的关注！<br/>
                    为了提供更优质、稳定的AI服务体验，我们的智能分析功能需要订阅后才能使用。
                </p>

                <div className="features">
                    <h3>订阅后您将享受到：</h3>
                    <ul>
                        <li>基于患者病情的智能对话分析和专业建议</li>
                        <li>智能医学影像报告审查与纠错</li>
                        <li>个性化报告内容优化建议与指导</li>
                        <li>持续的功能更新和优化</li>
                        <li>7x24小时稳定服务保障</li>
                    </ul>
                </div>

                <div className="buttons">
                    <button className="btn btn-primary" onClick={handleSubscribe}>
                        了解订阅方案
                    </button>
                    <button 
                        className={`btn btn-secondary ${(isTransitioning || isPending) ? 'disabled' : ''}`}
                        onClick={handleTrial}
                        disabled={isTransitioning || isPending}
                    >
                        {isTransitioning ? '正在跳转...' : '我要试用'}
                    </button>
                </div>

                <div className="footer">
                    <p>如有任何疑问，欢迎随时联系我们的客服团队</p>
                </div>
            </div>
        </div>
    );
}

export default SubscriptionPage;

