
import type { AIServiceConfig } from '../global';
import type { IStreamParser } from './streamParsers/IStreamParser';
import { StreamParserFactory } from './streamParsers/StreamParserFactory';

// 消息对象接口
interface ChatMessage {
    role: string;
    content: string | Array<{ type: string; text?: string; image_url?: { url: string } }>;
    images?: string[];
}

// 流式响应处理器接口
export interface IStreamHandler {
    onStart?: () => void;
    onMessage?: (data: { reasoning: string, answer: string }) => void;
    onComplete?: () => void;
    onError?: (error: Error) => void;
}

// AI服务调用库
export class AIService {
    // 流式请求
    async streamRequest(
        service: AIServiceConfig,
        content: { 
            prompt: string, 
            system?: string, 
            context?: string, 
            history?: Array<{ role: string, content: string }>,
            images?: Array<{ type: string, data: string, format: string }>
        },
        handler: IStreamHand<PERSON>,
        signal?: AbortSignal,
        timeout: number = 30000
    ): Promise<void> {
        const controller = new AbortController();
        const timeoutId = setTimeout(() => {
            controller.abort();
        }, timeout);

        // 如果有外部signal，也监听它
        if (signal) {
            signal.addEventListener('abort', () => controller.abort());
        }

        try {
            const messages = [];
            // 系统消息，方法1：通过多个消息的方式
            // 有些模型可能不支持或有问题，比如：deepseek-ai/DeepSeek-R1-Distill-Qwen-7B
            if (content.system) {
                messages.push({ role: 'system', content: content.system });
            }
            if (content.context) {
                messages.push({ role: 'system', content: content.context });
            }
            // 系统消息，方法2：通过合并的方式（兼容所有模型）
            // let systemContent = '';
            // if (content.system) systemContent += content.system + '\n';
            // if (content.context) systemContent += content.context + '\n';
            // if (systemContent) {
            //     messages.push({ role: 'system', content: systemContent });
            // }

            // 历史对话
            if (content.history && content.history.length > 0) {
                messages.push(...content.history);
            }

            // 构建用户消息，支持多模态
            const userMessage: ChatMessage = { role: 'user', content: content.prompt };
            
            // 如果有图片且服务支持多模态，转换为多模态格式
            if (content.images && content.images.length > 0 && service.multimodal) {
                if (service.streamType === 'openai') {
                    // OpenAI Vision API 格式
                    userMessage.content = [
                        { type: 'text', text: content.prompt },
                        ...content.images.map(img => ({
                            type: 'image_url',
                            image_url: {
                                url: `data:${img.format};base64,${img.data}`
                            }
                        }))
                    ];
                } else if (service.streamType === 'ollama') {
                    // Ollama 多模态格式
                    userMessage.images = content.images.map(img => img.data);
                }
            }

            messages.push(userMessage);

            console.log(`messages: ${JSON.stringify(messages, null, 2)}`);
            
            const response = await fetch(service.url, {
                method: 'POST',
                headers: {
                    'Authorization': service.key || '',
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    model: service.model,
                    messages: messages,
                    stream: service.stream || true,
                }),
                signal: controller.signal
            });

            clearTimeout(timeoutId);

            if (!response.ok) {
                // 处理 HTTP-500 服务器内部错误，解析服务器错误原因
                if (response.status === 500) {
                    try {
                        const errorText = await response.text();
                        const errorData = JSON.parse(errorText);
                        if (errorData.desc) {
                            throw new Error(`服务器错误：${errorData.desc} (${service.url})`);
                        }
                    } catch (parseError) {
                        if (parseError instanceof SyntaxError) {
                            console.warn('HTTP-500 返回的响应体不是有效的 JSON 格式');
                        } else {
                            throw parseError;
                        }
                    }
                }
                throw new Error(`HTTP CODE ${response.status} ${response.statusText}`);
            }

            handler.onStart?.();

            const reader = response.body?.getReader();
            if (!reader) {
                throw new Error('Failed to get reader');
            }

            // 创建流解析器
            const streamParser: IStreamParser = StreamParserFactory.createParser(service.streamType);

            while (true) {
                // 读取数据
                const { done, value } = await reader.read();
                if (done) break;

                // 解析数据
                const chunk = new TextDecoder().decode(value, { stream: true });
                const data = streamParser.parse(chunk);

                // 回调给处理器
                handler.onMessage?.({
                    reasoning: data.reasoning,
                    answer: data.answer,
                });
            }

            handler.onComplete?.();
        } catch (error) {
            clearTimeout(timeoutId);
            console.error(`AIService streamRequest error:`, error);
            
            if (error instanceof DOMException && error.name === 'AbortError') {
                // 如果是AbortError且不是外部signal触发的，就认为是超时
                if (!signal?.aborted) {
                    handler.onError?.(new Error(`连接超时 (${timeout}ms) - ${service.url}`));
                    return;
                }
                console.log('请求被用户中止');
                return;
            }
            
            // 网络错误
            if (error instanceof TypeError && error.message.includes('fetch')) {
                handler.onError?.(new Error(`网络连接失败，请检查网络设置或服务器地址 (${service.url})`));
                return;
            }
            
            if (error instanceof Error && error.message.startsWith('HTTP CODE')) {
                handler.onError?.(new Error(`服务器响应错误：${error.message} (${service.url})`));
                return;
            }
            
            // 解析错误
            if (error instanceof SyntaxError) {
                handler.onError?.(new Error(`服务器返回数据格式错误 (${service.url})`));
                return;
            }
            
            // 未知错误
            const errorMessage = error instanceof Error ? error.message : String(error);
            handler.onError?.(new Error(`请求失败：${errorMessage} (${service.url})`));
        }
    }
}

export default AIService;
