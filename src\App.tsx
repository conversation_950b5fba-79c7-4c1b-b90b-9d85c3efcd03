import "./App.css";
import { FluentProvider, webLightTheme, webDarkTheme } from "@fluentui/react-components";
import { useState, useEffect, createContext } from "react";
import { BrowserRouter as Router, Routes, Route, Navigate } from "react-router-dom";
import MainFrame from "./components/MainFrame";
import SubscriptionPage from "./components/pages/SubscriptionPage";

// 创建主题上下文
interface ThemeContextType {
    isDarkTheme: boolean;
    toggleTheme: () => void;
}

// eslint-disable-next-line react-refresh/only-export-components
export const ThemeContext = createContext<ThemeContextType>({
    isDarkTheme: false,
    toggleTheme: () => {}
});

function App() {
    // 从localStorage读取主题偏好，默认为true（暗色主题）
    const [isDarkTheme, setIsDarkTheme] = useState<boolean>(() => {
        const saved = localStorage.getItem('theme-preference');
        return saved ? JSON.parse(saved) : true;
    });

    // 切换主题函数
    const toggleTheme = () => {
        setIsDarkTheme(prev => !prev);
    };

    // 保存主题偏好到localStorage
    useEffect(() => {
        localStorage.setItem('theme-preference', JSON.stringify(isDarkTheme));
    }, [isDarkTheme]);

    return (
        <ThemeContext.Provider value={{ isDarkTheme, toggleTheme }}>
            <FluentProvider theme={isDarkTheme ? webDarkTheme : webLightTheme} style={{ height: "100%" }}>
                <Router future={{ v7_relativeSplatPath: true, v7_startTransition: true }}>
                    <Routes>
                        <Route path="/" element={<Navigate to="/subscription" replace />} />
                        <Route path="/subscription" element={<SubscriptionPage />} />
                        <Route path="/app" element={<MainFrame />} />
                        <Route path="*" element={<Navigate to="/subscription" replace />} />
                    </Routes>
                </Router>
            </FluentProvider>
        </ThemeContext.Provider>
    );
}

export default App;


