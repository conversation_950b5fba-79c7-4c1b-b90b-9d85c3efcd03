'use strict';

const Config = {
    AIService: {
        url: 'https://api.siliconflow.cn/v1/chat/completions',
        key: 'Bearer sk-jslrxxnmyvuonrkqifpgnarspnwuywjorfhzkosfrkxuwwra',
        model: 'deepseek-ai/DeepSeek-R1-Distill-Qwen-7B',
        //model: 'deepseek-ai/DeepSeek-R1-0528-Qwen3-8B',
        //model: 'Qwen/Qwen3-8B',
        //model: 'Pro/deepseek-ai/DeepSeek-R1',     //16$   - Error 403，不支持赠费
        //model: 'deepseek-ai/DeepSeek-R1',         //16$
        stream: true,
        streamType: 'openai',     // openai | ollama
    },

    // AIService: {
    //     url: 'http://127.0.0.1:11434/api/chat',
    //     key: '',
    //     //model: 'gemma:7b',
    //     model: 'amsaravi/medgemma-4b-it:q8',
    //     stream: true,
    //     streamType: 'ollama',     // openai | ollama
    //     multimodal: true,
    // },

    // AIService: {
    //     url: 'http://192.168.31.40:11434/api/chat',
    //     key: '',
    //     //model: 'qwen3:32b',
    //     //model: 'jwang580/medgemma_27b_q8_0:latest',
    //     //model: 'amsaravi/medgemma-4b-it:q8',
    //     //model: 'puyangwang/medgemma-27b-it:q8',
    //     //model: 'Lingshu32B:latest',
    //     model: 'qwen2.5vl:32b',
    //     //model: 'deepseek-r1:32b',
    //     stream: true,
    //     streamType: 'ollama',     // openai | ollama
    //     multimodal: true,
    // },

    // AIService: {
    //     url: 'http://192.168.31.133:7788/api/chat/kbqa',
    //     key: '',
    //     model: 'qwen3:32b',
    //     stream: true,
    //     streamType: 'ollama',     // openai | ollama
    // },

    // AIService: {
    //     url: 'http://192.168.31.133:7788/api/chat/kbqa',
    //     key: 'Bearer sk-jslrxxnmyvuonrkqifpgnarspnwuywjorfhzkosfrkxuwwra',
    //     model: 'deepseek-ai/DeepSeek-R1-Distill-Qwen-7B',
    //     stream: true,
    //     streamType: 'openai',     // openai | ollama
    // },
};

window.Config = Config;
