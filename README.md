# Report Assist - AI 医学影像报告助手

一个基于 React + TypeScript + Vite 构建的智能医学影像报告工具，通过 AI 技术帮助放射科医生检查和改进医学影像报告的质量。

## 主要功能特性

- **AI 智能审查**：自动检测医学影像报告中的文字错误和逻辑错误
- **实时对话**：支持流式 AI 对话，提供实时反馈和建议
- **现代化界面**：基于 Fluent UI 的美观、响应式用户界面
- **主题切换**：支持亮色/暗色主题切换，适应不同工作环境
- **Markdown 渲染**：支持富文本格式的报告显示和代码高亮
- **可配置模板**：支持自定义提示模板，适应不同的审查需求
- **多 AI 服务支持**：兼容 OpenAI API 和 Ollama 等多种 AI 服务

## 技术栈

### 前端框架
- **React 18** - 现代化的用户界面库
- **TypeScript** - 类型安全的 JavaScript 超集
- **Vite** - 快速的前端构建工具

### UI 组件库
- **Fluent UI React Components** - Microsoft 的现代化 UI 组件库
- **Fluent UI React Icons** - 丰富的图标库

## 环境要求
- **Node.js**: >= 16.0.0
- **npm**: >= 8.0.0 或 **yarn**: >= 1.22.0
- **现代浏览器**: Chrome 88+, Firefox 85+, Safari 14+

## 安装和设置

### 1. 安装依赖
```bash
npm install
# 或者使用 yarn
yarn install
```

### 3. 配置 AI 服务
编辑 `public/config.js` 文件，配置您的 AI 服务：

```javascript
const Config = {
    AIService: {
        url: 'https://api.openai.com/v1/chat/completions',  // AI 服务 URL
        key: 'Bearer your-api-key',                         // API 密钥
        model: 'gpt-3.5-turbo',                            // 模型名称
        stream: true,                                       // 是否启用流式响应
        streamType: 'openai',                              // 流类型: 'openai' | 'ollama'
    },
};
```

### 4. 启动开发服务器
```bash
npm run dev
# 或者使用 yarn
yarn dev
```

应用将在 `http://localhost:5173` 启动。

## 配置说明

### AI 服务配置

在 `public/config.js` 中配置不同的 AI 服务：

#### OpenAI API 配置
```javascript
AIService: {
    url: 'https://api.openai.com/v1/chat/completions',
    key: 'Bearer sk-your-api-key',
    model: 'gpt-3.5-turbo',
    stream: true,
    streamType: 'openai',
}
```

#### Ollama 本地服务配置
```javascript
AIService: {
    url: 'http://localhost:11434/api/chat',
    key: '',
    model: 'qwen3:32b',
    stream: true,
    streamType: 'ollama',
}
```

### 提示模板配置

在 `public/prompt-templates.js` 中自定义提示模板：

```javascript
const PromptTemplates = [
    {
        name: 'reportCheck',
        title: '报告审查模板',
        prompt: '您的提示内容...',
        system: '系统提示...',
        onInit: function() {
            // 初始化逻辑
        },
        onApply: function(data) {
            // 应用结果的处理逻辑
        }
    }
];
```

## 开发指南
### 项目结构

```
report-assist/
├── public/                     # 静态资源目录
│   ├── config.js              # AI 服务配置文件
│   ├── prompt-templates.js    # 提示模板配置
│   └── vite.svg               # 应用图标
├── src/                       # 源代码目录
│   ├── components/            # React 组件
│   │   ├── common/           # 通用组件
│   │   ├── pages/            # 页面组件
│   │   │   ├── AIChat.tsx    # AI 聊天主界面
│   │   │   └── Test.tsx      # 测试页面
│   │   └── MainFrame.tsx     # 主框架组件
│   ├── libs/                 # 工具库
│   │   ├── AIService.ts      # AI 服务调用库
│   │   ├── Markdown.ts       # Markdown 渲染库
│   │   └── streamParsers/    # 流数据解析器
│   ├── assets/               # 静态资源
│   ├── App.tsx               # 应用根组件
│   ├── main.tsx              # 应用入口
│   └── global.d.ts           # 全局类型定义
├── package.json              # 项目依赖配置
├── vite.config.ts            # Vite 构建配置
├── tsconfig.json             # TypeScript 配置
└── eslint.config.js          # ESLint 配置
```

### 添加新的 AI 服务支持

1. 在 `src/libs/streamParsers/` 目录下创建新的解析器
2. 实现 `IStreamParser` 接口
3. 在 `StreamParserFactory.ts` 中注册新的解析器
4. 更新配置文件中的 `streamType` 选项

### 自定义提示模板

1. 编辑 `public/prompt-templates.js`
2. 添加新的模板对象
3. 实现 `onInit`、`onApply` 等回调函数
4. 重启开发服务器以加载新模板

### 扩展 UI 组件

项目使用 Fluent UI 组件库，可以：
- 查阅 [Fluent UI 文档](https://react.fluentui.dev/)
- 使用现有的设计系统和组件
- 保持界面风格的一致性

### 代码规范

项目使用 ESLint 进行代码质量检查：
```bash
npm run lint         # 检查代码规范
npm run lint -- --fix  # 自动修复可修复的问题
```

## 生产构建和部署

### 开发模式
```bash
npm run dev          # 启动开发服务器
npm run lint         # 运行代码检查
```

### 生产构建
```bash
npm run build        # 构建生产版本
npm run preview      # 预览生产构建
```

构建产物将生成在 `dist/` 目录中，可以部署到任何静态文件服务器。
