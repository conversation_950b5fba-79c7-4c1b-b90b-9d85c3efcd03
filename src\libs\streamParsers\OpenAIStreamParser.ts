import type { IStreamParser } from './IStreamParser';

export class OpenAIStreamParser implements IStreamParser {
    private reasoningBuffer = '';   // 推理内容
    private answerBuffer = '';      // 回答内容

    parse(chunk: string): { reasoning: string, answer: string } {
        try {

            // OpenAI 流式响应格式处理
            // 格式: data: {...}\n\ndata: {...}\n\n
            const lines = chunk.split('\n');
            for (const line of lines) {
                // 跳过空行
                if (line.trim() === '')
                    continue;

                // 跳过心跳消息
                if (line === 'data: [DONE]')
                    continue;

                // 处理数据行
                if (line.startsWith('data: ')) {
                    const jsonStr = line.replace('data: ', '');
                    const data = JSON.parse(jsonStr);

                    // 提取推理内容
                    const reasoning = data.choices[0]?.delta?.reasoning_content || '';
                    if (reasoning) {
                        this.reasoningBuffer += reasoning;
                    }

                    // 提取回答内容
                    const answer = data.choices[0]?.delta?.content || '';
                    if (answer) {
                        this.answerBuffer += answer;
                    }

                    // 检查是否完成
                    if (data.choices[0]?.finish_reason)
                        break;
                }
            }

            return { reasoning: this.reasoningBuffer, answer: this.answerBuffer };
        } catch (error) {
            console.error('OpenAI 流解析错误:', error);
            return { reasoning: this.reasoningBuffer, answer: this.answerBuffer };
        }
    }
}