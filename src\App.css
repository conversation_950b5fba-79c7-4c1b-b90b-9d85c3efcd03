
/* 页面过渡动画 */
.page-transition-enter {
    opacity: 0;
    transform: translateX(100px);
}

.page-transition-enter-active {
    opacity: 1;
    transform: translateX(0);
    transition: opacity 300ms ease-in-out, transform 300ms ease-in-out;
}

.page-transition-exit {
    opacity: 1;
    transform: translateX(0);
}

.page-transition-exit-active {
    opacity: 0;
    transform: translateX(-100px);
    transition: opacity 300ms ease-in-out, transform 300ms ease-in-out;
}

/* 确保路由容器占满全屏 */
#root {
    height: 100vh;
    width: 100vw;
}

/* 全局样式重置 */
* {
    box-sizing: border-box;
}

html, body {
    margin: 0;
    padding: 0;
    height: 100%;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
}

