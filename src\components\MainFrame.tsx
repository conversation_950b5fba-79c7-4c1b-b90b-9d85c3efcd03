import { useState, useContext, useEffect } from "react";
import { makeStyles, Tab, TabList, type TabValue, type SelectTabEvent, type SelectTabData, Button, Tooltip } from "@fluentui/react-components";
import { WeatherMoon24Regular, WeatherSunny24Regular } from "@fluentui/react-icons";
import { ThemeContext } from "../App";
import AIChat from "./pages/AIChat";
// eslint-disable-next-line @typescript-eslint/no-unused-vars
import Test from "./pages/Test";
import highlightLight from "../assets/highlight/github.min.css?url";
import highlightDark from "../assets/highlight/github-dark.min.css?url";

const useStyles = makeStyles({
    container: {
        height: "100%",
        display: "flex",
        flexDirection: "column",
    },
    header: {
        display: "flex",
        justifyContent: "space-between",
        alignItems: "center",
        padding: "0 10px",
        borderBottom: "1px solid var(--colorNeutralStroke2)",
        backgroundColor: "var(--colorNeutralBackground2)",
    },
    tabList: {
        height: "40px",
    },
    themeToggle: {
        minWidth: "40px",
    },
    tabContent: {
        flexGrow: 1,
        overflow: "hidden",
    },
});

function MainFrame() {
    const styles = useStyles();
    const [selectedTab, setSelectedTab] = useState<TabValue>("aichat");
    const { isDarkTheme, toggleTheme } = useContext(ThemeContext);

    // 初始化 highlight.js 主题
    useEffect(() => {
        const oldLink = document.querySelector("link[data-hljs-theme]");
        if (oldLink) oldLink.remove();

        const link = document.createElement("link");
        link.rel = "stylesheet";
        link.href = isDarkTheme ? highlightDark : highlightLight;
        link.setAttribute("data-hljs-theme", "true");
        document.head.appendChild(link);
    }, [isDarkTheme]);

    const onTabSelect = (_event: SelectTabEvent, data: SelectTabData) => {
        setSelectedTab(data.value);
    };

    // 扩展切换主题函数
    const handleToggleTheme = () => {
        toggleTheme();

        // 直接更新 highlight.js 样式
        const oldLink = document.querySelector("link[data-hljs-theme]");
        if (oldLink) oldLink.remove();

        const link = document.createElement("link");
        link.rel = "stylesheet";
        link.href = !isDarkTheme ? highlightDark : highlightLight;
        link.setAttribute("data-hljs-theme", "true");
        document.head.appendChild(link);
    };

    return (
        <div className={styles.container}>
            <div className={styles.header}>
                <TabList selectedValue={selectedTab} onTabSelect={onTabSelect} className={styles.tabList}>
                    <Tab value="aichat">AIChat</Tab>
                    {/* <Tab value="test">Test</Tab> */}
                </TabList>

                <Tooltip content={isDarkTheme ? "亮色主题" : "暗色主题"} relationship="label">
                    <Button
                        appearance="subtle"
                        icon={isDarkTheme ? <WeatherSunny24Regular /> : <WeatherMoon24Regular />}
                        onClick={handleToggleTheme} // 使用新的处理函数
                        className={styles.themeToggle}
                        aria-label={isDarkTheme ? "亮色主题" : "暗色主题"}
                    />
                </Tooltip>
            </div>

            <div className={styles.tabContent}>
                <div style={{ display: selectedTab === "aichat" ? "block" : "none", height: "100%" }}>
                    <AIChat />
                </div>
                {/* <div style={{ display: selectedTab === "test" ? "block" : "none", height: "100%" }}>
                    <Test />
                </div> */}
            </div>
        </div>
    );
}

export default MainFrame;
