import { useRef, useEffect } from "react";
import { But<PERSON> } from "@fluentui/react-components";
import { Markdown } from "../../libs/Markdown";

function Test() {
    const md = useRef(new Markdown());
    const contentRef = useRef<HTMLDivElement>(null);
    const markdown = `
# report-assist
### Project setup
\`\`\`javascript
console.log('hello world');
\`\`\`

\`\`\`text?title=影像描述&name=report_desc&type=report&code-apply-btn=true
这里是影像描述内容正文...
第二段文字
\`\`\`

### Customize configuration
See [Configuration Reference](https://cli.vuejs.org/config/).
`;

    const handleClickMarkdown = () => {
        const html = md.current.render(markdown);
        if (contentRef.current) {
            contentRef.current.innerHTML = html;
            md.current.bindEventHandlers();
        }
    };

    useEffect(() => {
        handleClickMarkdown();
    }, []);

    return (
        <div>
            <Button onClick={handleClickMarkdown}>渲染 Markdown</Button>
            <div ref={contentRef} />
        </div>
    );
};

export default Test;

