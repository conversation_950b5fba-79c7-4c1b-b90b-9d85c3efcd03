/* eslint-disable @typescript-eslint/no-unused-vars */
import React, { useState, useEffect, useRef } from "react";
import {
    Button,
    Toaster,
    useToastController,
    type MessageBarIntent,
    ToastTitle,
    Toast,
    Accordion,
    AccordionItem,
    AccordionHeader,
    AccordionPanel,
    Link,
    Text,
} from "@fluentui/react-components";
import { 
    Send24Regular, 
    Send24Filled, 
    Stop24Regular, 
    Stop24Filled, 
    Delete24Regular, 
    Delete24Filled, 
    Thinking24Regular, 
    Thinking24Filled,
    Image24Regular,
    Dismiss24Regular
} from "@fluentui/react-icons";
import { AIService } from "../../libs/AIService";
import { Markdown } from "../../libs/Markdown";
import type { PromptTemplate, ChatImage, ChatItem } from "../../global";
import styles from "./AIChat.module.css";
import EmptyData from "../common/EmptyData";

// 计算思考使用时间
function useElapsedTime(startTime: number | undefined, isActive: boolean) {
    const [elapsedSeconds, setElapsedSeconds] = useState(0);

    useEffect(() => {
        if (!startTime || !isActive) {
            return;
        }

        // 思考中...启动定时器实时更新
        const updateElapsed = () => {
            setElapsedSeconds(Math.floor((Date.now() - startTime) / 1000));
        };

        updateElapsed(); // 立即更新
        const interval = setInterval(updateElapsed, 1000);
        return () => clearInterval(interval);
    }, [startTime, isActive]);

    return elapsedSeconds;
}

// 思考标题组件
function ThinkingTitle({ chat, isConnecting }: { chat: ChatItem; isConnecting: boolean }) {
    const elapsedSeconds = useElapsedTime(chat.startTime, chat.isThinking || false);

    if (isConnecting) {
        return "正在连接...";
    }

    if (!chat.startTime) return "";

    const finalElapsed = chat.endTime ? Math.floor((chat.endTime - chat.startTime) / 1000) : elapsedSeconds;
    return chat.isThinking ? `正在思考...（用时: ${elapsedSeconds} 秒）` : `已深度思考（用时: ${finalElapsed} 秒）`;
}

function AIChat() {
    const { dispatchToast } = useToastController();
    const [chatlist, setChatlist] = useState<ChatItem[]>([]);
    const [prompt, setPrompt] = useState("");
    const [selectedImages, setSelectedImages] = useState<ChatImage[]>([]);
    const [isUploadingImages, setIsUploadingImages] = useState(false);
    const isConnecting = useRef(false);
    const isChatting = useRef(false);
    const [chatHistory, setChatHistory] = useState<Array<{ role: string; content: string }>>([]);
    const [currentAssistantMessage, setCurrentAssistantMessage] = useState("");
    const fileInputRef = useRef<HTMLInputElement>(null);

    const service = useRef(new AIService());
    const md = useRef(new Markdown());
    const abortController = useRef<AbortController | null>(null);
    const maxHistoryLength = 3;
    const promptTemplates = window.PromptTemplates || [];
    const isEmpty = chatlist.length === 0;

    // 检查当前配置是否支持多模态
    const isMultimodalSupported = window.Config?.AIService?.multimodal === true;

    // 图片处理相关常量
    const MAX_IMAGE_SIZE = 10 * 1024 * 1024; // 10MB
    const MAX_IMAGE_COUNT = 5;
    const SUPPORTED_FORMATS = ['image/jpeg', 'image/png', 'image/gif', 'image/webp'];

    // 图片处理函数
    const handleImageSelect = async (event: React.ChangeEvent<HTMLInputElement>) => {
        const files = Array.from(event.target.files || []);
        if (files.length === 0) return;

        if (selectedImages.length + files.length > MAX_IMAGE_COUNT) {
            showMessage(`最多只能选择 ${MAX_IMAGE_COUNT} 张图片`, "warning");
            return;
        }

        setIsUploadingImages(true);
        const newImages: ChatImage[] = [];

        try {
            for (const file of files) {
                // 检查文件格式
                if (!SUPPORTED_FORMATS.includes(file.type)) {
                    showMessage(`不支持的图片格式: ${file.name}`, "warning");
                    continue;
                }

                // 检查文件大小
                if (file.size > MAX_IMAGE_SIZE) {
                    showMessage(`图片 ${file.name} 超过 10MB 限制`, "warning");
                    continue;
                }

                // 转换为 base64
                const base64 = await fileToBase64(file);
                const preview = URL.createObjectURL(file);

                newImages.push({
                    id: `img_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
                    file,
                    base64,
                    preview,
                });
            }

            setSelectedImages(prev => [...prev, ...newImages]);
        } catch (error) {
            showMessage(`图片处理失败: ${error}`, "error");
        } finally {
            setIsUploadingImages(false);
            if (fileInputRef.current) {
                fileInputRef.current.value = '';
            }
        }
    };

    const fileToBase64 = (file: File): Promise<string> => {
        return new Promise((resolve, reject) => {
            const reader = new FileReader();
            reader.onload = () => {
                const result = reader.result as string;
                resolve(result.split(',')[1]); // 移除 data:image/...;base64, 前缀
            };
            reader.onerror = reject;
            reader.readAsDataURL(file);
        });
    };

    const removeImage = (imageId: string) => {
        setSelectedImages(prev => {
            const updated = prev.filter(img => img.id !== imageId);
            // 清理预览 URL
            const removedImage = prev.find(img => img.id === imageId);
            if (removedImage) {
                URL.revokeObjectURL(removedImage.preview);
            }
            return updated;
        });
    };

    const clearAllImages = () => {
        selectedImages.forEach(img => URL.revokeObjectURL(img.preview));
        setSelectedImages([]);
    };

    // 清理组件卸载时的预览 URL
    useEffect(() => {
        return () => {
            selectedImages.forEach(img => URL.revokeObjectURL(img.preview));
        };
    }, []);

    const showMessage = (message: string, intent: MessageBarIntent = "info") => {
        const getStatusColors = (intent: MessageBarIntent) => {
            switch (intent) {
                case "success":
                    return {
                        border: "var(--colorStatusSuccessBorder2)",
                        background: "var(--colorStatusSuccessBackground1)",
                        foreground: "var(--colorStatusSuccessForeground1)",
                    };
                case "warning":
                    return {
                        border: "var(--colorStatusWarningBorder2)",
                        background: "var(--colorStatusWarningBackground1)",
                        foreground: "var(--colorStatusWarningForeground1)",
                    };
                case "error":
                    return {
                        border: "var(--colorStatusDangerBorder2)",
                        background: "var(--colorStatusDangerBackground1)",
                        foreground: "var(--colorStatusDangerForeground1)",
                    };
                default:
                    return {
                        border: "var(--colorNeutralStroke1Selected)",
                        background: "var(--colorNeutralBackground1Selected)",
                        foreground: "var(--colorNeutralForeground1Selected)",
                    };
            }
        };

        const colors = getStatusColors(intent);
        dispatchToast(
            <Toast
                style={{
                    border: `1px solid ${colors.border}`,
                    boxShadow: "0 6px 16px rgba(0, 0, 0, 0.2)",
                    backgroundColor: colors.background,
                }}
            >
                <ToastTitle
                    style={{
                        fontWeight: "600",
                        color: colors.foreground,
                    }}
                >
                    {message}
                </ToastTitle>
            </Toast>,
            { intent, position: "top", timeout: intent === "error" ? 1000 * 30 : 1000 * 10 }
        );
    };

    const mainRef = useRef<HTMLDivElement>(null);

    const scrollToBottom = () => {
        if (mainRef.current) {
            mainRef.current.scrollTop = mainRef.current.scrollHeight;
        }
    };

    const isScrollAtBottom = () => {
        if (mainRef.current) {
            const threshold = 30;
            return mainRef.current.scrollHeight - mainRef.current.scrollTop - mainRef.current.clientHeight < threshold;
        }
        return false;
    };

    const handleTemplateClick = (template: PromptTemplate) => {
        if (isConnecting.current || isChatting.current) {
            showMessage("请等待当前操作完成", "warning");
            return;
        }

        template.onInit?.(null);
        emptyContent();
        startChatWithTemplate(template);
    };

    const emptyContent = () => {
        setChatlist([]);
        setPrompt("");
        setChatHistory([]);
        setCurrentAssistantMessage("");
        clearAllImages();
    };

    const startChatWithTemplate = async (template: PromptTemplate) => {
        // 检查是否有图片但模型不支持多模态
        if (selectedImages.length > 0 && !isMultimodalSupported) {
            showMessage("当前模型不支持图片输入，请切换到支持多模态的模型", "warning");
            return;
        }

        const newChat: ChatItem = {
            promptTemplate: { ...template, title: template.title || template.prompt },
            reasoningHtml: "",
            answerHtml: "",
            isThinking: true,
            images: selectedImages.length > 0 ? [...selectedImages] : undefined,
        };

        // 初始化上下文
        if (window.PromptContext?.onInit) {
            window.PromptContext.onInit();
        }

        const promptContext = window.PromptContext?.context || "";

        // 设置聊天列表及连接状态
        setChatlist((prev) => [...prev, newChat]);
        isConnecting.current = true;
        isChatting.current = true;
        abortController.current = new AbortController();

        // 滚动到底部
        setTimeout(() => {
            scrollToBottom();
        }, 0);

        try {
            const aiConfig = { ...window.Config.AIService };

            // 构建包含图片的消息内容
            const messageContent = selectedImages.length > 0 && isMultimodalSupported
                ? {
                    prompt: template.prompt,
                    system: template.system,
                    context: promptContext,
                    history: chatHistory,
                    images: selectedImages.map(img => ({
                        type: 'image',
                        data: img.base64,
                        format: img.file.type
                    }))
                }
                : {
                    prompt: template.prompt,
                    system: template.system,
                    context: promptContext,
                    history: chatHistory,
                };

            await service.current.streamRequest(
                aiConfig,
                messageContent,
                {
                    onStart: () => {
                        isConnecting.current = false;
                        newChat.startTime = Date.now();
                        setPrompt("");
                        setCurrentAssistantMessage("");
                        clearAllImages(); // 发送后清空图片
                    },
                    onMessage: (data: { reasoning: string; answer: string }) => {
                        setCurrentAssistantMessage(data.answer);
                        setChatlist((prev) => {
                            if (prev.length > 0) {
                                const lastIndex = prev.length - 1;
                                const updatedLastItem = {
                                    ...prev[lastIndex],
                                    reasoningHtml: md.current.render(data.reasoning),
                                    answerHtml: md.current.render(data.answer),
                                    isThinking: !data.answer || data.answer.trim() === "", // 在没有答案之前，显示思考状态
                                };
                                return [...prev.slice(0, lastIndex), updatedLastItem];
                            }
                            return prev;
                        });

                        // 绑定事件处理器并保持滚动位置
                        const wasAtBottom = isScrollAtBottom();
                        setTimeout(() => {
                            md.current.bindEventHandlers({
                                onApplyCallback: template.onApply,
                            });

                            if (wasAtBottom) {
                                scrollToBottom();
                            }
                        }, 0);
                    },
                    onComplete: () => {
                        isConnecting.current = false;
                        isChatting.current = false;

                        setChatlist((prev) => {
                            if (prev.length > 0) {
                                const lastIndex = prev.length - 1;
                                const updatedLastItem = { ...prev[lastIndex], endTime: Date.now(), isThinking: false };
                                return [...prev.slice(0, lastIndex), updatedLastItem];
                            }
                            return prev;
                        });
                        setChatHistory((prev) => {
                            const newHistory = [...prev, { role: "user", content: template.prompt }, { role: "assistant", content: currentAssistantMessage }];
                            return newHistory.slice(-maxHistoryLength);
                        });

                        template.onResult?.(currentAssistantMessage);
                    },
                    onError: (error: Error) => {
                        isConnecting.current = false;
                        isChatting.current = false;

                        setChatlist((prev) => {
                            if (prev.length > 0) {
                                const lastIndex = prev.length - 1;
                                const updatedLastItem = { ...prev[lastIndex], endTime: Date.now(), isThinking: false };
                                return [...prev.slice(0, lastIndex), updatedLastItem];
                            }
                            return prev;
                        });
                        showMessage(`请求失败: ${error.message}`, "error");
                    },
                },
                abortController.current.signal
            );
        } catch (error) {
            isConnecting.current = false;
            isChatting.current = false;
            showMessage(`启动聊天失败: ${error}`, "error");
        }
    };

    const toggleChat = () => {
        if (isConnecting.current) return;

        if (isChatting.current) {
            abortController.current?.abort();
            isChatting.current = false;
            isConnecting.current = false;

            // 停止当前聊天的思考状态
            setChatlist((prev) => {
                if (prev.length > 0) {
                    const lastIndex = prev.length - 1;
                    const updatedLastItem = { ...prev[lastIndex], isThinking: false };
                    return [...prev.slice(0, lastIndex), updatedLastItem];
                }
                return prev;
            });
        } else {
            startChatWithTemplate({
                name: "",
                title: "",
                prompt: prompt,
                system: "",
            });
        }
    };

    const handleKeyDown = (event: React.KeyboardEvent) => {
        if (event.key === "Enter" && !event.shiftKey) {
            event.preventDefault();
            toggleChat();
        }
    };

    return (
        <div className={styles.container}>
            <Toaster offset={{ horizontal: 0, vertical: -15 }} />
            <div ref={mainRef} className={styles.main}>
                {isEmpty ? (
                    <div className={styles.empty}>
                        <EmptyData style={{ flex: 1 }} />
                        <Text size={500}>在这里您可以：</Text>
                        <div className={styles.templateList}>
                            {promptTemplates.map((template) => (
                                <div key={template.name}>
                                    <Link onClick={() => handleTemplateClick(template)}>{template.title || template.prompt}</Link>
                                </div>
                            ))}
                            <Text>更多功能正在开发中...✨</Text>
                        </div>
                    </div>
                ) : (
                    <div>
                        {chatlist.map((chat, index) => (
                            <div key={index} className={styles.chatItem}>
                                <div className={styles.promptTitle}>
                                    <div>
                                        <Text style={{ whiteSpace: "pre-wrap" }}>{chat.promptTemplate?.title}</Text>
                                        {chat.images && chat.images.length > 0 && (
                                            <div className={styles.chatImages}>
                                                {chat.images.map((image) => (
                                                    <img
                                                        key={image.id}
                                                        src={image.preview}
                                                        alt="Chat image"
                                                        className={styles.chatImageThumbnail}
                                                        title={image.file.name}
                                                    />
                                                ))}
                                            </div>
                                        )}
                                    </div>
                                </div>
                                <Accordion defaultOpenItems={["reasoning"]} collapsible>
                                    <AccordionItem value="reasoning">
                                        <AccordionHeader icon={<Thinking24Filled />} size="small" className={styles.accordionHeader}>
                                            <ThinkingTitle chat={chat} isConnecting={isConnecting.current} />
                                        </AccordionHeader>
                                        <AccordionPanel>
                                            <div className={styles.reasoning} dangerouslySetInnerHTML={{ __html: chat.reasoningHtml }} />
                                        </AccordionPanel>
                                    </AccordionItem>
                                </Accordion>
                                <div className={styles.answer} dangerouslySetInnerHTML={{ __html: chat.answerHtml }} />
                            </div>
                        ))}
                    </div>
                )}
            </div>

            <div className={styles.footer}>
                <textarea 
                    className={styles.textarea}
                    rows={4}
                    placeholder={"请输入您的问题...\r\n(Shift+Entry换行)"}
                    value={prompt} 
                    onChange={(e) => setPrompt(e.target.value)} 
                    onKeyDown={handleKeyDown} 
                />
                
                {/* 附件栏 - 仅在支持多模态时显示 */}
                {isMultimodalSupported && (selectedImages.length > 0 || isUploadingImages) && (
                    <div className={styles.attachmentBar}>
                        <div className={styles.imagePreviewContainer}>
                            {selectedImages.map((image) => (
                                <div key={image.id} className={styles.imagePreview}>
                                    <img src={image.preview} alt="Preview" className={styles.previewImage} title={image.file.name} />
                                    <Button
                                        size="small"
                                        appearance="subtle"
                                        icon={<Dismiss24Regular />}
                                        onClick={() => removeImage(image.id)}
                                        className={styles.removeImageButton}
                                        title="删除图片"
                                    />
                                </div>
                            ))}
                            {isUploadingImages && (
                                <div className={styles.uploadingIndicator}>
                                    <Text size={200}>上传中...</Text>
                                </div>
                            )}
                        </div>
                    </div>
                )}

                <div className={styles.toolbar}>
                    <div className={styles.leftToolbar}>
                        {/* 图片上传按钮 - 仅在支持多模态时显示 */}
                        {isMultimodalSupported && (
                            <>
                                <input
                                    ref={fileInputRef}
                                    type="file"
                                    accept="image/*"
                                    multiple
                                    onChange={handleImageSelect}
                                    className={styles.hiddenFileInput}
                                    aria-label="选择图片文件"
                                    title="选择图片文件进行上传"
                                />
                                <Button
                                    size="small"
                                    appearance="transparent"
                                    icon={<Image24Regular />}
                                    onClick={() => fileInputRef.current?.click()}
                                    disabled={isConnecting.current || isChatting.current || selectedImages.length >= MAX_IMAGE_COUNT}
                                    title={`添加图片 (${selectedImages.length}/${MAX_IMAGE_COUNT})`}
                                >
                                    图片
                                </Button>
                            </>
                        )}
                    </div>
                    <div>
                        <Button 
                            onClick={emptyContent} 
                            disabled={isConnecting.current || isChatting.current} 
                            icon={<Delete24Regular />} 
                            style={{ marginRight: "8px" }}
                        >
                            清空
                        </Button>
                        <Button
                            appearance={isChatting.current ? "secondary" : "primary"}
                            onClick={toggleChat}
                            disabled={isConnecting.current || (!isChatting.current && prompt === "" && selectedImages.length === 0)}
                            icon={isChatting.current ? <Stop24Filled /> : <Send24Filled />}
                        >
                            {isChatting.current ? "停止" : "发送"}
                        </Button>
                    </div>
                </div>
            </div>
        </div>
    );
}

export default AIChat;
