import type { IStreamParser } from './IStreamParser';
import { OpenAIStreamParser } from './OpenAIStreamParser';
import { OllamaStreamParser } from './OllamaStreamParser';

export class StreamParserFactory {
    static createParser(type: string): IStreamParser {
        switch (type.toLowerCase()) {
            case 'openai':
                return new OpenAIStreamParser();
            case 'ollama':
                return new OllamaStreamParser();
            default:
                console.warn(`未知的流解析器类型: ${type}，使用默认的OpenAI解析器`);
                return new OpenAIStreamParser();
        }
    }
}
